<template>
  <view class="test-page">
    <view class="header">
      <text class="title">问题数据转换测试</text>
    </view>

    <view class="section">
      <text class="section-title">原始API数据：</text>
      <view class="data-box">
        <text class="data-text">{{ JSON.stringify(apiData, null, 2) }}</text>
      </view>
    </view>

    <view class="section">
      <text class="section-title">转换后的数据：</text>
      <view class="data-box">
        <text class="data-text">{{ JSON.stringify(convertedData, null, 2) }}</text>
      </view>
    </view>

    <view class="section">
      <text class="section-title">VideoQuiz组件预览：</text>
      <VideoQuiz 
        :questions="convertedData.questions" 
        :rewardAmount="100" 
        :videoCompleted="true" 
        @submit="onQuizSubmit" 
        @complete="onQuizComplete" 
      />
    </view>

    <button class="test-btn" @click="testConversion">重新测试转换</button>
  </view>
</template>

<script>
import VideoQuiz from '@/components/VideoQuiz.vue'

export default {
  name: 'TestQuiz',
  components: {
    VideoQuiz
  },
  data() {
    return {
      // 模拟API返回的问题数据（基于文档中的示例）
      apiData: [
        {
          "questionText": "视频中提到的主要观点是什么？",
          "orderNum": 0,
          "options": [
            {
              "optionText": "A. 提高工作效率",
              "isCorrect": true,
              "orderNum": 0
            },
            {
              "optionText": "B. 降低成本",
              "isCorrect": false,
              "orderNum": 1
            },
            {
              "optionText": "C. 增加收入",
              "isCorrect": false,
              "orderNum": 2
            }
          ]
        },
        {
          "questionText": "视频中建议的最佳实践是？",
          "orderNum": 1,
          "options": [
            {
              "optionText": "A. 每天工作12小时",
              "isCorrect": false,
              "orderNum": 0
            },
            {
              "optionText": "B. 合理安排时间",
              "isCorrect": true,
              "orderNum": 1
            },
            {
              "optionText": "C. 减少休息时间",
              "isCorrect": false,
              "orderNum": 2
            }
          ]
        }
      ],
      convertedData: { questions: [] }
    }
  },
  mounted() {
    this.testConversion()
  },
  methods: {
    // 复制视频页面中的问题数据处理逻辑
    processQuizData(apiQuestions) {
      if (!apiQuestions || !Array.isArray(apiQuestions) || apiQuestions.length === 0) {
        console.log('没有问题数据');
        return { questions: [] };
      }

      console.log('开始处理问题数据:', apiQuestions);

      try {
        const questions = apiQuestions.map((apiQuestion) => {
          // 生成选项标识符 A, B, C, D...
          const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];
          
          // 转换选项格式
          const options = (apiQuestion.options || []).map((apiOption, optionIndex) => ({
            id: optionLabels[optionIndex] || optionIndex.toString(),
            text: apiOption.optionText || apiOption.text || ''
          }));

          // 找到正确答案的标识符
          let correctAnswer = '';
          const correctOptionIndex = (apiQuestion.options || []).findIndex(option => option.isCorrect);
          if (correctOptionIndex >= 0) {
            correctAnswer = optionLabels[correctOptionIndex] || correctOptionIndex.toString();
          }

          return {
            question: apiQuestion.questionText || apiQuestion.question || '',
            options: options,
            correctAnswer: correctAnswer
          };
        });

        return { questions };
      } catch (error) {
        console.error('处理问题数据失败:', error);
        return { questions: [] };
      }
    },

    testConversion() {
      console.log('测试数据转换...');
      this.convertedData = this.processQuizData(this.apiData);
      console.log('转换结果:', this.convertedData);
    },

    onQuizSubmit(answers) {
      console.log('用户提交答案:', answers);
      uni.showToast({
        title: '答案已提交',
        icon: 'success'
      });
    },

    onQuizComplete(result) {
      console.log('答题完成:', result);
      uni.showToast({
        title: `答题完成，得分：${result.correctCount}/${result.totalCount}`,
        icon: 'success'
      });
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.section {
  margin-bottom: 30px;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  display: block;
}

.data-box {
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.data-text {
  font-family: monospace;
  font-size: 12px;
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
}

.test-btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 15px 30px;
  font-size: 16px;
  margin: 20px auto;
  display: block;
}

.test-btn:active {
  background-color: #0056b3;
}
</style>
