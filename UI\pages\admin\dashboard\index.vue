<template>
	<view class="container">
		<!-- 欢迎卡片 -->
		<view class="welcome-card">
			<view class="welcome-header">
				<view class="welcome-info">
					<text class="welcome-title">欢迎回来，管理员</text>
					<text class="welcome-subtitle">今天是 {{ currentDate }}，祝您工作愉快！</text>
				</view>
				<view class="welcome-avatar">
					<image src="/assets/images/avatar-placeholder.png" mode="aspectFill"></image>
				</view>
			</view>

			<view class="quick-stats">
				<view class="quick-stat-item">
					<text class="stat-value">{{ statisticsData.totalUsers }}</text>
					<text class="stat-label">总用户数</text>
				</view>
				<view class="quick-stat-item">
					<text class="stat-value">{{ statisticsData.newUsersToday }}</text>
					<text class="stat-label">今日新增</text>
				</view>
				<view class="quick-stat-item">
					<text class="stat-value">{{ statisticsData.viewerCount }}</text>
					<text class="stat-label">观看人数</text>
				</view>
				<view class="quick-stat-item">
					<text class="stat-value">{{ rewardAmount }}元</text>
					<text class="stat-label">总奖励金额</text>
				</view>
			</view>
		</view>

		<!-- 功能导航 -->
		<view class="nav-section">
			<view class="section-title">功能导航</view>
			<view class="nav-grid">
				<view class="nav-item" @tap="navigateTo('/pages/admin/media/index')">
					<view class="nav-icon icon-media">
						<text class="iconfont icon-media"></text>
					</view>
					<text class="nav-text">素材库</text>
				</view>
				<view class="nav-item" @tap="navigateTo('/pages/admin/videos/index')">
					<view class="nav-icon icon-video">
						<text class="iconfont icon-video"></text>
					</view>
					<text class="nav-text">视频管理</text>
				</view>

				<view class="nav-item" @tap="navigateTo('/pages/admin/users/index')">
					<view class="nav-icon icon-user">
						<text class="iconfont icon-user"></text>
					</view>
					<text class="nav-text">用户管理</text>
				</view>
				<view class="nav-item" @tap="showComingSoon">
					<view class="nav-icon icon-settings">
						<text class="iconfont icon-settings"></text>
					</view>
					<text class="nav-text">系统设置</text>
				</view>
			</view>
		</view>

		<!-- 数据概览 -->
		<view class="data-section">
			<view class="section-header">
				<text class="section-title">数据概览</text>
				<text class="section-more" @tap="showFullStats">查看详情 ></text>
			</view>

			<view class="data-cards">
				<!-- 关键指标卡片 -->
				<view class="data-card">
					<view class="card-header">
						<text class="card-title">关键指标</text>
						<text class="card-subtitle">今日数据</text>
					</view>
					<view class="metrics-grid">
						<view class="metric-item">
							<text class="metric-value">{{ statisticsData.completeRate.toFixed(2) }}%</text>
							<text class="metric-label">完成率</text>
						</view>
						<view class="metric-item">
							<text class="metric-value">{{ statisticsData.correctRate.toFixed(2) }}%</text>
							<text class="metric-label">正确率</text>
						</view>
						<view class="metric-item">
							<text class="metric-value">{{ statisticsData.answerUserCount }}</text>
							<text class="metric-label">答题用户</text>
						</view>
						<view class="metric-item">
							<text class="metric-value">{{ statisticsData.totalOrders }}</text>
							<text class="metric-label">订单数</text>
						</view>
					</view>
				</view>

				<!-- 用户活跃度卡片 -->
				<view class="data-card">
					<view class="card-header">
						<text class="card-title">用户活跃度</text>
						<text class="card-subtitle">最近7天</text>
					</view>
					<view class="chart-placeholder">
						<view class="bar-chart">
							<view class="bar-item" v-for="(item, index) in activityData" :key="index">
								<view class="bar-value" :style="{ height: item.height }"></view>
								<text class="bar-label">{{ item.day }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 标签统计卡片 -->
				<view class="data-card" v-if="dashboardData.tagStatistics && dashboardData.tagStatistics.length > 0">
					<view class="card-header">
						<text class="card-title">用户标签分布</text>
						<text class="card-subtitle">用户分类</text>
					</view>
					<view class="tag-list">
						<view class="tag-item" v-for="(tag, index) in dashboardData.tagStatistics.slice(0, 5)"
							:key="index">
							<view class="tag-info">
								<text class="tag-name">{{ tag.tagName }}</text>
								<text class="tag-count">{{ tag.userCount }}人</text>
							</view>
							<text class="tag-percentage">{{ tag.percentage.toFixed(1) }}%</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 最近活动 -->
		<view class="activity-section">
			<view class="section-header">
				<text class="section-title">最近活动</text>
				<text class="section-more" @tap="showAllActivities">查看全部 ></text>
			</view>

			<view class="activity-list">
				<view class="activity-item" v-for="(activity, index) in recentActivities" :key="index">
					<view class="activity-icon" :class="'activity-' + activity.type">
						<text class="iconfont" :class="'icon-' + activity.type"></text>
					</view>
					<view class="activity-content">
						<text class="activity-title">{{ activity.title }}</text>
						<text class="activity-desc">{{ activity.description }}</text>
						<text class="activity-time">{{ activity.time }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>

import { getAllStatistics } from '../../../api/dashboard.js';
import { getStatisticsOverview, getDailyTrend } from '../../../api/statistics.js';
import adminAuthService from '../../../utils/adminAuthService.js';

export default {
	data () {
		return {
			// 统计数据 - 使用新的API数据结构
			statisticsData: {
				totalUsers: 0,
				newUsersToday: 0,
				totalOrders: 0,
				viewerCount: 0,
				completeRate: 0,
				answerUserCount: 0,
				correctRate: 0,
				totalRewardAmount: 0,
				untaggedUserCount: 0
			},

			// 概览数据
			overviewData: {
				totalViewCount: 0,
				totalCompleteViewCount: 0,
				totalAnswerCount: 0,
				totalCorrectAnswerCount: 0,
				totalRewardCount: 0,
				totalRewardAmountYuan: 0
			},

			// 趋势数据
			trendData: [],

			// 仪表板数据
			dashboardData: {
				tagStatistics: [],
				courseStatistics: {},
				answerStatistics: {},
				rewardStatistics: {},
				orderStatistics: {}
			},

			currentDate: '',
			videoCount: 0,
			quizCount: 0,
			rewardAmount: 0,

			// 活跃度数据 - 将从API获取
			activityData: [
				{ day: '周一', height: '30%' },
				{ day: '周二', height: '45%' },
				{ day: '周三', height: '60%' },
				{ day: '周四', height: '40%' },
				{ day: '周五', height: '75%' },
				{ day: '周六', height: '55%' },
				{ day: '周日', height: '35%' }
			],

			// 热门视频 - 将从API获取
			hotVideos: [],

			// 最近活动 - 将从API获取
			recentActivities: [
				{
					type: 'user',
					title: '新用户注册',
					description: '用户001完成了注册',
					time: '10分钟前'
				},
				{
					type: 'video',
					title: '视频上传',
					description: '管理员上传了新视频《健康生活小知识》',
					time: '30分钟前'
				},
				{
					type: 'quiz',
					title: '问题回答',
					description: '用户002回答了问题并获得奖励',
					time: '1小时前'
				},
				{
					type: 'reward',
					title: '红包发放',
					description: '系统自动发放了3个红包，总金额3.00元',
					time: '2小时前'
				}
			],

			// 加载状态
			loading: false
		}
	},
	async onLoad () {
		// 获取当前日期
		this.setCurrentDate();

		// 检查管理员权限
		if (!adminAuthService.isLoggedIn() || !adminAuthService.isSessionValid()) {
			adminAuthService.redirectToLogin();
			return;
		}

		const userType = adminAuthService.getUserType();
		if (userType !== 'admin' && userType !== 'manager' && userType !== 'agent') {
			uni.showToast({
				title: '无权限访问',
				icon: 'none'
			});
			uni.navigateBack();
			return;
		}

		// 加载仪表板数据
		await this.loadDashboardData();
	},
	methods: {
		// 加载仪表板数据
		async loadDashboardData () {
			try {
				this.loading = true;
				uni.showLoading({
					title: '加载数据中...'
				});

				// 并行获取所有需要的数据
				const [allStatsResponse, overviewResponse, trendResponse] = await Promise.all([
					getAllStatistics(),
					getStatisticsOverview(),
					getDailyTrend({ startDate: this.getDateDaysAgo(7), endDate: this.getCurrentDate() })
				]);

				// 处理统计数据
				if (allStatsResponse && allStatsResponse.keyMetrics) {
					this.statisticsData = {
						...allStatsResponse.keyMetrics,
						totalUsers: allStatsResponse.keyMetrics.totalMembers || 0,
						newUsersToday: allStatsResponse.keyMetrics.todayNewMembers || 0
					};
				}

				// 处理概览数据
				if (overviewResponse.success && overviewResponse.data) {
					this.overviewData = overviewResponse.data;
					this.videoCount = overviewResponse.data.totalViewCount || 0;
					this.quizCount = overviewResponse.data.totalAnswerCount || 0;
					this.rewardAmount = overviewResponse.data.totalRewardAmountYuan || 0;
				}

				// 处理趋势数据
				if (trendResponse.success && trendResponse.data) {
					this.trendData = trendResponse.data;
					this.updateActivityChart();
				}

				// 处理仪表板数据
				if (allStatsResponse && allStatsResponse.dashboard) {
					this.dashboardData = allStatsResponse.dashboard;
				}

				uni.hideLoading();
			} catch (error) {
				console.error('加载仪表板数据失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: '数据加载失败',
					icon: 'none'
				});

				// 数据加载失败，显示空数据
				this.statisticsData = {};
				this.overviewData = {};
				this.videoCount = 0;
				this.quizCount = 0;
				this.rewardAmount = 0;
				this.dashboardData = {};
			} finally {
				this.loading = false;
			}
		},



		// 更新活跃度图表
		updateActivityChart () {
			if (this.trendData && this.trendData.length > 0) {
				const maxValue = Math.max(...this.trendData.map(item => item.activeUserCount));
				this.activityData = this.trendData.slice(-7).map((item, index) => {
					const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
					return {
						day: days[index] || `第${index + 1}天`,
						height: `${Math.round((item.activeUserCount / maxValue) * 100)}%`
					};
				});
			}
		},

		// 获取当前日期
		getCurrentDate () {
			const date = new Date();
			return date.toISOString().split('T')[0];
		},

		// 获取几天前的日期
		getDateDaysAgo (days) {
			const date = new Date();
			date.setDate(date.getDate() - days);
			return date.toISOString().split('T')[0];
		},

		setCurrentDate () {
			const date = new Date();
			const year = date.getFullYear();
			const month = date.getMonth() + 1;
			const day = date.getDate();
			this.currentDate = `${year}年${month}月${day}日`;
		},
		navigateTo (url) {
			uni.navigateTo({
				url: url
			});
		},
		showComingSoon () {
			uni.showToast({
				title: '功能开发中，敬请期待',
				icon: 'none'
			});
		},
		showFullStats () {
			uni.showToast({
				title: '完整统计功能开发中',
				icon: 'none'
			});
		},
		showAllActivities () {
			uni.showToast({
				title: '活动记录功能开发中',
				icon: 'none'
			});
		}
	}
}
</script>

<style lang="scss">
@import '@/styles/index.scss';

.container {
	@extend .page-container;
	padding: $page-padding;
}

/* 欢迎卡片优化 */
.welcome-card {
	background: linear-gradient(135deg, #186BFF 0%, #40a9ff 50%, #69c0ff 100%);
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	color: #fff;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(24, 144, 255, 0.25);
	position: relative;
	overflow: hidden;
}

/* 欢迎卡片背景装饰 */
.welcome-card::before {
	content: '';
	position: absolute;
	top: -50%;
	right: -20%;
	width: 200rpx;
	height: 200rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 50%;
	transform: rotate(45deg);
}

.welcome-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.welcome-info {
	flex: 1;
}

.welcome-title {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.welcome-subtitle {
	font-size: 24rpx;
	opacity: 0.8;
}

.welcome-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50rpx;
	overflow: hidden;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.welcome-avatar image {
	width: 100%;
	height: 100%;
}

.quick-stats {
	display: flex;
	justify-content: space-between;
	background-color: rgba(255, 255, 255, 0.2);
	border-radius: 16rpx;
	padding: 20rpx;
}

.quick-stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.quick-stat-item .stat-value {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 6rpx;
}

.quick-stat-item .stat-label {
	font-size: 22rpx;
	opacity: 0.8;
}

/* 功能导航 */
.nav-section {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 30rpx;
	color: #333;
}

.nav-grid {
	display: flex;
	flex-wrap: wrap;
}

.nav-item {
	width: 33.33%;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 40rpx;
}

.nav-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 16rpx;
}

.nav-icon .iconfont {
	font-size: 46rpx;
	color: #fff;
}

.icon-media {
	background-color: #722ed1;
}

.icon-video {
	background-color: #186BFF;
}

.icon-quiz {
	background-color: #13c2c2;
}

.icon-reward {
	background-color: #faad14;
}

.icon-user {
	background-color: #eb2f96;
}

.icon-settings {
	background-color: #52c41a;
}

.nav-text {
	font-size: 26rpx;
	color: #333;
}

/* 数据概览 */
.data-section {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-more {
	font-size: 24rpx;
	color: #186BFF;
}

/* 数据卡片优化 */
.data-cards {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.data-card {
	background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(0, 0, 0, 0.04);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.data-card:hover {
	transform: translateY(-4rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

.data-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4rpx;
	background: linear-gradient(90deg, #186BFF, #52c41a, #faad14);
	opacity: 0;
	transition: opacity 0.3s;
}

.data-card:hover::before {
	opacity: 1;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.card-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.card-subtitle {
	font-size: 22rpx;
	color: #999;
}

.chart-placeholder {
	height: 300rpx;
	display: flex;
	align-items: flex-end;
}

.bar-chart {
	display: flex;
	justify-content: space-between;
	width: 100%;
	height: 100%;
}

.bar-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 12%;
}

.bar-value {
	width: 100%;
	background-color: #186BFF;
	border-radius: 6rpx 6rpx 0 0;
}

.bar-label {
	margin-top: 10rpx;
	font-size: 22rpx;
	color: #999;
}

.rank-list {
	display: flex;
	flex-direction: column;
}

.rank-item {
	display: flex;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.rank-item:last-child {
	border-bottom: none;
}

.rank-number {
	width: 50rpx;
	font-size: 28rpx;
	font-weight: bold;
	color: #999;
}

.rank-number.top-rank {
	color: #ff4d4f;
}

.rank-title {
	flex: 1;
	font-size: 26rpx;
	color: #333;
	margin-right: 20rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.rank-value {
	font-size: 26rpx;
	color: #186BFF;
	font-weight: bold;
}

/* 最近活动 */
.activity-section {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.activity-list {
	display: flex;
	flex-direction: column;
}

.activity-item {
	display: flex;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.activity-item:last-child {
	border-bottom: none;
}

.activity-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 20rpx;
}

.activity-icon .iconfont {
	font-size: 40rpx;
	color: #fff;
}

.activity-user {
	background-color: #eb2f96;
}

.activity-video {
	background-color: #186BFF;
}

.activity-quiz {
	background-color: #13c2c2;
}

.activity-reward {
	background-color: #faad14;
}

.activity-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.activity-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 6rpx;
}

.activity-desc {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 6rpx;
}

.activity-time {
	font-size: 22rpx;
	color: #999;
}

/* 新增样式 - 关键指标网格 */
.metrics-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
	padding: 20rpx;
}

.metric-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 15rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
}

.metric-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #186BFF;
	margin-bottom: 8rpx;
}

.metric-label {
	font-size: 22rpx;
	color: #666;
}

/* 标签统计样式 */
.tag-list {
	padding: 20rpx;
}

.tag-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.tag-item:last-child {
	border-bottom: none;
}

.tag-info {
	display: flex;
	flex-direction: column;
}

.tag-name {
	font-size: 26rpx;
	color: #333;
	margin-bottom: 4rpx;
}

.tag-count {
	font-size: 22rpx;
	color: #666;
}

.tag-percentage {
	font-size: 24rpx;
	font-weight: bold;
	color: #186BFF;
}
</style>